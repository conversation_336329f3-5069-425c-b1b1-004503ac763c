import {Flex, Divider, Typography} from 'antd';
import {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {css} from '@emotion/css';
import {MCPApplicationLink} from '@/links/mcp';
import {MCPApplication} from '@/types/mcp/application';
import TagGroup from '@/components/MCP/TagGroup';
import MCPCard from '@/design/MCP/MCPCard';
import UpdateInfo from '@/components/MCP/UpdateInfo';

const noshrink = css`
    flex-shrink: 0;
    flex-grow: 0;
`;

const overflowHidden = css`
    overflow: hidden;
`;

const containerCss = css`
    padding: 16px 20px;
`;

interface AppCardProps {
    item: MCPApplication;
}

const AppCard = ({item}: AppCardProps) => {
    const navigate = useNavigate();

    const handleClick = useCallback(
        () => {
            navigate(
                MCPApplicationLink.toUrl({
                    workspaceId: item.workspaceId,
                    applicationId: item.id,
                })
            );
        },
        [item.id, item.workspaceId, navigate]
    );

    return (
        <MCPCard vertical onClick={handleClick} className={containerCss}>
            <Typography.Title level={4} ellipsis>{item.name}</Typography.Title>
            <Typography.Paragraph
                ellipsis={{rows: 2}}
                type="secondary"
                style={{height: 44, margin: '12px 0 8px'}}
            >
                {item.description || '暂无描述'}
            </Typography.Paragraph>

            {item.servers?.length ? (
                <Flex align="center" className={overflowHidden}>
                    <span className={noshrink}>已订阅</span>
                    <Divider type="vertical" />
                    <TagGroup
                        color="info"
                        labels={item.servers?.map(server => ({id: server.id, label: server.name}))}
                        style={{flex: 1, overflow: 'hidden'}}
                    />
                </Flex>
            ) : (
                <span style={{color: 'var(--color-gray-6)'}}>暂无订阅的MCP Server</span>
            )}
            <Divider style={{margin: '12px 0 8px', borderColor: 'rgba(75, 108, 159, 0.15)'}} />
            <UpdateInfo username={item.modifiedUser} time={item.modifiedTime} />
        </MCPCard>
    );
};

export default AppCard;
