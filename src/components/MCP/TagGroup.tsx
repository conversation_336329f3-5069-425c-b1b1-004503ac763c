/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Flex, Tooltip} from 'antd';
import {Tag, TagColor} from '@panda-design/components';
import {CSSProperties, ReactNode, useLayoutEffect, useMemo, useRef, useState} from 'react';

interface Label {
    id: number;
    label: string;
}
const TooltipTitleWrapper = styled.div`
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 4px 0;
`;

const COLOR_MAP: Record<string, CSSProperties> = {
    'light-purple': {
        backgroundColor: '#EBE6F9',
        color: '#8264DE',
    },
    'gray': {
        backgroundColor: '#E2E8F0',
        color: '#4B6C9F',
    },
};

const TagClassName = 'mcp-server-card-tag';

interface Props {
    prefix?: ReactNode;
    color?: TagColor;
    labels: Label[];
    maxNum?: number;
    style?: CSSProperties;
    gap?: number;
}

const ExtraTag = (
    {labels, innerColor, colorStyle}:
    {labels: Label[], innerColor: TagColor, colorStyle: CSSProperties}
) => {
    if (labels.length === 0) {
        return null;
    }
    return (
        <Tooltip
            title={
                <TooltipTitleWrapper>
                    {labels.map(label => {
                        return (
                            <Tag
                                type="flat"
                                color={innerColor}
                                key={label.id}
                                style={{
                                    margin: 0,
                                    ...colorStyle,
                                }}
                            >
                                {label.label}
                            </Tag>
                        );
                    })}
                </TooltipTitleWrapper>
            }
        >
            <Tag
                className={TagClassName}
                type="flat"
                color={innerColor}
                style={{
                    margin: 0,
                    ...colorStyle,
                }}
            >
                +{labels.length}
            </Tag>
        </Tooltip>
    );
};

/**
 * @param maxNum 如果外部指定了该参数，组件将不会根据容器宽度自行决定显示多少tag
 */
const TagGroup = ({labels, color = 'info', style, gap = 8, maxNum}: Props) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const [labelCounts, setLabelCounts] = useState<number>(maxNum ? maxNum : (labels?.length || 0));
    const colorStyle = COLOR_MAP[color];
    const innerColor = COLOR_MAP[color] ? undefined : color;

    const isEmpty = !labels || labels.length === 0;

    const showingLabels = useMemo(
        () => {
            if (isEmpty) {
                return [];
            }
            return labels.slice(0, labelCounts);
        },
        [labels, labelCounts, isEmpty]
    );
    const extraLables = useMemo(
        () => {
            if (isEmpty) {
                return [];
            }
            return labels.slice(labelCounts);
        },
        [labels, labelCounts, isEmpty]
    );
    useLayoutEffect(
        () => {
            // 在本地开发时，useLayoutEffect会被调用两次，但第二次调用不是所期待的。故使用extraLables判断。
            if (isEmpty || extraLables.length > 0 || maxNum) {
                return;
            }

            const ele = containerRef.current;
            if (ele) {
                const containerWidth = Number(ele.offsetWidth);
                const tags = ele.querySelectorAll(`.${TagClassName}`);
                let tagWidth = 0;
                const extraTagMaxWidth = 45;
                let maxShowingTagCounts = 0;
                for (let index = 0; index < tags.length; index++) {
                    // offsetWidth取值是经过浏览器四舍五入后的，直接使用有可能造成结果差一两个像素，导致元素遮盖，故加一取一个安全值。
                    const curWidth = Number((tags[index] as HTMLDivElement).offsetWidth) + 1;
                    tagWidth = tagWidth + curWidth + (index === 0 ? 0 : gap);
                    if (tagWidth >= (containerWidth - extraTagMaxWidth - gap)) {
                        window.console.log('containerWidth', containerWidth, 'tagWidth', tagWidth, 'index', index);
                        break;
                    } else {
                        maxShowingTagCounts = index + 1;
                    }
                }
                setLabelCounts(maxShowingTagCounts);
            }
        },
        [gap, setLabelCounts, isEmpty, labelCounts, extraLables, maxNum]
    );

    if (isEmpty) {
        return (
            <Flex style={{zIndex: 1, gap, height: 22, ...style}} align="center" ref={containerRef}>
                <Tag
                    type="flat"
                    color="gray"
                    style={{
                        margin: 0,
                        flexShrink: 0,
                        backgroundColor: '#F5F5F5',
                        color: '#999999',
                        cursor: 'default',
                        pointerEvents: 'none',
                    }}
                >
                    暂无标签
                </Tag>
            </Flex>
        );
    }

    return (
        <Flex style={{zIndex: 1, gap, height: 22, ...style}} align="center" ref={containerRef}>
            {
                showingLabels.map(label => {
                    return (
                        <Tag
                            className={TagClassName}
                            type="flat"
                            color={innerColor}
                            key={label.id}
                            style={{
                                margin: 0,
                                flexShrink: 0,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                ...colorStyle,
                            }}
                        >
                            {label.label}
                        </Tag>
                    );
                })
            }
            <ExtraTag labels={extraLables} innerColor={innerColor} colorStyle={colorStyle} />
        </Flex>
    );
};

export default TagGroup;

