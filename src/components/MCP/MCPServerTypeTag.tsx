import {Tag} from '@panda-design/components';
import {MCPServerType} from '@/types/mcp/mcp';
import {IconLocal, IconRemote, IconStandard} from '@/icons/mcp';

export const SERVER_TYPE_DICT: Record<MCPServerType, string> = {
    script: 'Local',
    openapi: 'Remote',
    external: '标准MCP',
};

export const getServerTypeText = (type: MCPServerType) => {
    return SERVER_TYPE_DICT[type] || SERVER_TYPE_DICT.external;
};

export const getServerTypeIcon = (type: MCPServerType) => {
    switch (type) {
        case 'script':
            return <IconLocal />;
        case 'openapi':
            return <IconRemote />;
        case 'external':
            return <IconStandard />;
        default:
            return <IconStandard />;
    }
};

interface Props {
    type: MCPServerType;
}
export default function MCPServerTypeTag({type}: Props) {
    return (
        <Tag
            type="flat"
            style={{
                color: '#3779EA',
                backgroundColor: '#DFE9FC',
                margin: 0,
            }}
            icon={getServerTypeIcon(type)}
        >
            {SERVER_TYPE_DICT[type]}
        </Tag>
    );
}
